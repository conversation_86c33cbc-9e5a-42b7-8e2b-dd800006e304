"use client"

import { useEffect, useState } from "react"
import { <PERSON> } from "wouter"
import { motion, LayoutGroup } from "framer-motion"
import { TextRotate } from "@/components/ui/text-rotate"
import { But<PERSON> } from "@/components/ui/button"
import Floating, { FloatingElement } from "@/components/ui/parallax-floating"

const emmaImages = [
  {
    url: "/Anuncio.png",
    title: "Anuncio Publicitario",
    alt: "Anuncio"
  },
  {
    url: "/Astronauta.png",
    title: "Astronauta",
    alt: "Astronauta"
  },
  {
    url: "/Gato lentes.webp",
    title: "Gato con Lentes",
    alt: "Gato Lentes"
  },
  {
    url: "/Hombre real.jpeg",
    title: "Hombre Real",
    alt: "Hombre Real"
  },
  {
    url: "/Labial.jpg",
    title: "Labial",
    alt: "Labial"
  },
  {
    url: "/PHOTO-2025-06-30-19-02-40.jpg",
    title: "Foto Profesional",
    alt: "Foto Profesional"
  },
  {
    url: "/libro.jpg",
    title: "Libro",
    alt: "Libro"
  },
  {
    url: "/poster.jpg",
    title: "Poster",
    alt: "Poster"
  }
]

export function HeroSection() {
  return (
    <section className="w-full h-screen overflow-hidden md:overflow-visible flex flex-col items-center justify-center relative bg-white min-h-[100vh]">
      {/* Floating Images Background */}
      <Floating
        sensitivity={-0.5}
        className="absolute inset-0 w-full h-full z-10 pointer-events-none"
        style={{
          isolation: 'isolate',
          width: '100vw',
          height: '100vh',
          position: 'absolute',
          top: 0,
          left: 0
        }}
      >
        {/* Floating Images - Balanced Radial Distribution */}
        {/* Top Left Quadrant */}
        <FloatingElement
          depth={0.5}
          className="z-[11] floating-img-1"
          style={{ top: '15%', left: '8%' }}
        >
          <motion.img
            src={emmaImages[0].url}
            alt={emmaImages[0].alt}
            className="w-16 h-12 sm:w-24 sm:h-18 md:w-28 md:h-20 lg:w-32 lg:h-24 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform -rotate-[8deg] shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          />
        </FloatingElement>

        {/* Top Center */}
        <FloatingElement
          depth={1}
          className="z-[12] floating-img-2"
          style={{ top: '8%', left: '50%', transform: 'translateX(-50%)' }}
        >
          <motion.img
            src={emmaImages[1].url}
            alt={emmaImages[1].alt}
            className="w-24 h-18 sm:w-32 sm:h-24 md:w-40 md:h-30 lg:w-48 lg:h-36 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform rotate-[5deg] shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          />
        </FloatingElement>

        {/* Bottom Left */}
        <FloatingElement
          depth={4}
          className="z-[15] floating-img-3"
          style={{ bottom: '15%', left: '12%' }}
        >
          <motion.img
            src={emmaImages[2].url}
            alt={emmaImages[2].alt}
            className="w-28 h-28 sm:w-36 sm:h-36 md:w-44 md:h-44 lg:w-52 lg:h-52 object-cover -rotate-[12deg] hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
          />
        </FloatingElement>

        {/* Top Right Quadrant */}
        <FloatingElement
          depth={2}
          className="z-[13] floating-img-4"
          style={{ top: '15%', right: '8%' }}
        >
          <motion.img
            src={emmaImages[3].url}
            alt={emmaImages[3].alt}
            className="w-20 h-16 sm:w-28 sm:h-22 md:w-36 md:h-28 lg:w-44 lg:h-32 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[15deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.1 }}
          />
        </FloatingElement>

        {/* Bottom Right */}
        <FloatingElement
          depth={1}
          className="z-[12] floating-img-5"
          style={{ bottom: '15%', right: '12%' }}
        >
          <motion.img
            src={emmaImages[4].url}
            alt={emmaImages[4].alt}
            className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[8deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.3 }}
          />
        </FloatingElement>

        {/* Middle Left */}
        <FloatingElement
          depth={3}
          className="z-[14] floating-img-6"
          style={{ top: '50%', left: '4%', transform: 'translateY(-50%)' }}
        >
          <motion.img
            src={emmaImages[5].url}
            alt={emmaImages[5].alt}
            className="w-18 h-14 sm:w-24 sm:h-18 md:w-32 md:h-24 lg:w-36 lg:h-28 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[12deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          />
        </FloatingElement>

        {/* Middle Right */}
        <FloatingElement
          depth={2.5}
          className="z-[13] floating-img-7"
          style={{ top: '50%', right: '4%', transform: 'translateY(-50%)' }}
        >
          <motion.img
            src={emmaImages[6].url}
            alt={emmaImages[6].alt}
            className="w-20 h-16 sm:w-28 sm:h-20 md:w-36 md:h-28 lg:w-40 lg:h-32 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[10deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.7 }}
          />
        </FloatingElement>

        {/* Bottom Center */}
        <FloatingElement
          depth={1.5}
          className="z-[12] floating-img-8"
          style={{ bottom: '8%', left: '50%', transform: 'translateX(-50%)' }}
        >
          <motion.img
            src={emmaImages[7].url}
            alt={emmaImages[7].alt}
            className="w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[6deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.9 }}
          />
        </FloatingElement>

        {/* Top Left Inner */}
        <FloatingElement
          depth={3.5}
          className="z-[15] floating-img-9"
          style={{ top: '25%', left: '20%' }}
        >
          <motion.video
            autoPlay
            loop
            muted
            playsInline
            className="w-20 h-20 sm:w-28 sm:h-28 md:w-36 md:h-36 lg:w-44 lg:h-44 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[18deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.1 }}
          >
            <source src="/video gato brincando.mp4" type="video/mp4" />
          </motion.video>
        </FloatingElement>
      </Floating>

      {/* Main Content */}
      <div className="flex flex-col justify-center items-center w-[280px] sm:w-[350px] md:w-[550px] lg:w-[750px] xl:w-[850px] z-50 relative pointer-events-auto px-4 sm:px-6 md:px-8">
        <motion.h1
          className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl text-center w-full justify-center items-center flex-col flex whitespace-pre leading-tight font-bold tracking-tight space-y-1 md:space-y-2 lg:space-y-3"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2, ease: "easeOut", delay: 0.3 }}
        >
          <span>Haz tu </span>
          <LayoutGroup>
            <motion.span layout className="flex whitespace-pre">
              <motion.span
                layout
                className="flex whitespace-pre"
                transition={{ type: "spring", damping: 30, stiffness: 400 }}
              >
                marketing{" "}
              </motion.span>
              <TextRotate
                texts={[
                  "increíble",
                  "viral",
                  "exitoso ✨",
                  "único",
                  "🚀 potente",
                  "💎 premium",
                  "profesional",
                  "🔥 épico",
                  "imparable",
                  "⚡ rápido",
                  "automático",
                  "inteligente",
                  "rentable 💰",
                ]}
                mainClassName="overflow-hidden pr-3 text-[#3018ef] py-0 pb-2 md:pb-4 rounded-xl"
                staggerDuration={0.03}
                staggerFrom="last"
                rotationInterval={3000}
                transition={{ type: "spring", damping: 30, stiffness: 400 }}
              />
            </motion.span>
          </LayoutGroup>
        </motion.h1>

        <motion.p
          className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-center font-normal pt-3 sm:pt-4 md:pt-6 lg:pt-8 text-gray-600 max-w-4xl mx-auto leading-relaxed"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2, ease: "easeOut", delay: 0.5 }}
        >
          con la primera agencia virtual de marketing del mundo.
          <br />
          IA especializada, resultados reales, disponible 24/7.
        </motion.p>

        <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 items-center mt-6 sm:mt-8 md:mt-10 lg:mt-12 text-xs">
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{
              duration: 0.2,
              ease: "easeOut",
              delay: 0.7,
              scale: { duration: 0.2 },
            }}
            whileHover={{
              scale: 1.05,
              transition: { type: "spring", damping: 30, stiffness: 400 },
            }}
          >
            <Link href="/login">
              <Button variant="red" size="lg" className="text-sm sm:text-base md:text-lg font-semibold tracking-tight px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-3 rounded-full z-20 shadow-2xl w-full sm:w-auto">
                Empezar Ahora <span className="font-serif ml-1">→</span>
              </Button>
            </Link>
          </motion.div>

          <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 20 }}
            transition={{
              duration: 0.2,
              ease: "easeOut",
              delay: 0.7,
              scale: { duration: 0.2 },
            }}
            whileHover={{
              scale: 1.05,
              transition: { type: "spring", damping: 30, stiffness: 400 },
            }}
          >
            <Link href="#demo">
              <Button variant="blue" size="lg" className="text-sm sm:text-base md:text-lg font-semibold tracking-tight px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-3 rounded-full z-20 shadow-2xl w-full sm:w-auto">
                ★ Ver Demo
              </Button>
            </Link>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
